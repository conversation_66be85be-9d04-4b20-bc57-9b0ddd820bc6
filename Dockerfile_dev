FROM debian:bookworm

WORKDIR /root/memo

RUN apt-get update \
    && apt-get install -y --force-yes --no-install-recommends \
    build-essential \
    libdebuginfod-dev \
    libunwind-dev \
    liblz4-dev \
    pkg-config \
    python3-dev \
    python3-dbg \
    python3-pip \
    python3-venv \
    make \
    cmake \
    gdb \
    valgrind \
    lcov \
    nodejs \
    npm \
    clang-format \
    git \
    ccache \
    wget \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

 RUN git clone https://github.com/mem0ai/mem0.git . \
     && git checkout 64c3d34deb056aff01126b855f1f0b655fd86ad1

 RUN python3 -m venv /root/memo/venv \
     && . /root/memo/venv/bin/activate \
     && /root/memo/venv/bin/pip install --upgrade pip \
     && /root/memo/venv/bin/pip install poetry

RUN /root/memo/venv/bin/pip install torch --index-url https://download.pytorch.org/whl/cpu
RUN /root/memo/venv/bin/pip install pinecone pinecone-text

 RUN . /root/memo/venv/bin/activate \
     && make install_all
 RUN . /root/memo/venv/bin/activate \
     && make build
 RUN . /root/memo/venv/bin/activate \
     && /root/memo/venv/bin/pip install pre-commit ruff isort
 RUN . /root/memo/venv/bin/activate \
     && /root/memo/venv/bin/pre-commit install --install-hooks
 # Set bash profile to automatically activate virtualenv
 RUN echo 'source /root/memo/venv/bin/activate' >> /root/.bashrc